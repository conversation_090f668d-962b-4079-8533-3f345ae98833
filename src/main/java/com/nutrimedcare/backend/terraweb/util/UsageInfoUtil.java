package com.nutrimedcare.backend.terraweb.util;

import com.nutrimedcare.backend.terraweb.dao.medicine.MedicationRecordEntity;
import com.nutrimedcare.backend.terraweb.dao.medicine.MedicineInfoEntity;
import com.nutrimedcare.backend.terraweb.dto.medicine.MedicineRecordDetailDTO;
import com.nutrimedcare.backend.terraweb.dto.medicine.MedicineUsageDTO;
import com.nutrimedcare.backend.terraweb.enums.medicine.MedicineUsageTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.hutool.core.math.Fraction;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.nutrimedcare.backend.terraweb.util.FractionUtil.convert2Fraction;

/**
 * <AUTHOR>
 */
@Slf4j
public class UsageInfoUtil {

    private UsageInfoUtil() {
    }


    public static Fraction calWeekConsumeNum(MedicineInfoEntity medicineInfo, MedicationRecordEntity medicationRecord) {
        try {
            // 获取 record 中用法对应 usageInfo
            List<MedicineRecordDetailDTO> usageDetailList = medicationRecord.getUsageDetail();

            // 主用法消耗
            Fraction mainConsumeFraction = calRealWeekConsumeNum(medicineInfo.getDosage(), medicineInfo.getUsageInfo(), usageDetailList.get(0));
            // 特殊用法消耗
            List<MedicineUsageDTO> specialUsageList = medicineInfo.getExtInfo().getSpecialUsageList();
            List<Fraction> specialConsumeFractionList = new ArrayList<>();
            for (int i = 0; i < specialUsageList.size(); i++) {
                MedicineUsageDTO specialUsage = specialUsageList.get(i);
                MedicineRecordDetailDTO medicineRecordDetailDTO = usageDetailList.get(i + 1);
                specialConsumeFractionList.add(calRealWeekConsumeNum(specialUsage.getDosageInfo(), specialUsage.getUsageType(), medicineRecordDetailDTO));
            }
            specialConsumeFractionList.forEach(mainConsumeFraction::add);
            return mainConsumeFraction;
        } catch (Exception e) {
            log.warn(e.getMessage());
            return Fraction.ZERO;
        }
    }


    private static Fraction calRealWeekConsumeNum(String dosage, Integer usageInfo, MedicineRecordDetailDTO usageDetail) {
        if (usageDetail == null || usageDetail.getUsageInfo() == null) {
            return calWeekConsumeNum(dosage, usageInfo);
        }
        MedicineUsageDTO recordUsageInfo = usageDetail.getUsageInfo();
        if (StringUtils.isBlank(recordUsageInfo.getDosageInfo()) || Objects.isNull(recordUsageInfo.getUsageType())) {
            return calWeekConsumeNum(dosage, usageInfo);
        }
        return calWeekConsumeNum(recordUsageInfo.getDosageInfo(), recordUsageInfo.getUsageType());
    }


    public static Fraction calWeekConsumeNum(String dosage, Integer usageInfo) {
        if (MedicineUsageTypeEnum.QD.getType().equals(usageInfo)) {
            return FractionUtil.convert2Fraction(dosage).multiplyBy(Fraction.of(7, 1));
        }
        if (MedicineUsageTypeEnum.BID.getType().equals(usageInfo)) {
            return FractionUtil.convert2Fraction(dosage).multiplyBy(Fraction.of(2 * 7, 1));
        }
        if (MedicineUsageTypeEnum.TID.getType().equals(usageInfo)) {
            return FractionUtil.convert2Fraction(dosage).multiplyBy(Fraction.of(3 * 7, 1));
        }
        if (MedicineUsageTypeEnum.QID.getType().equals(usageInfo)) {
            return FractionUtil.convert2Fraction(dosage).multiplyBy(Fraction.of(4 * 7, 1));
        }
        if (MedicineUsageTypeEnum.QOD.getType().equals(usageInfo)) {
            return FractionUtil.convert2Fraction(dosage).multiplyBy(Fraction.of(7, 2));
        }
        if (MedicineUsageTypeEnum.QW.getType().equals(usageInfo)) {
            return FractionUtil.convert2Fraction(dosage).multiplyBy(Fraction.of(1, 1));
        }
        if (MedicineUsageTypeEnum.BIW.getType().equals(usageInfo)) {
            return FractionUtil.convert2Fraction(dosage).multiplyBy(Fraction.of(2, 1));
        }
        if (MedicineUsageTypeEnum.Q2H.getType().equals(usageInfo)) {
            return FractionUtil.convert2Fraction(dosage).multiplyBy(Fraction.of(12 * 7, 1));
        }
        if (MedicineUsageTypeEnum.Q8H.getType().equals(usageInfo)) {
            return FractionUtil.convert2Fraction(dosage).multiplyBy(Fraction.of(3 * 7, 1));
        }
        if (MedicineUsageTypeEnum.N.getType().equals(usageInfo)) {
            return FractionUtil.convert2Fraction(dosage).multiplyBy(Fraction.of(7, 1));
        }
        if (MedicineUsageTypeEnum.QAM.getType().equals(usageInfo)) {
            return FractionUtil.convert2Fraction(dosage).multiplyBy(Fraction.of(7, 1));
        }
        if (MedicineUsageTypeEnum.QPM.getType().equals(usageInfo)) {
            return FractionUtil.convert2Fraction(dosage).multiplyBy(Fraction.of(7, 1));
        }
        if (MedicineUsageTypeEnum.QNOON.getType().equals(usageInfo)) {
            return FractionUtil.convert2Fraction(dosage).multiplyBy(Fraction.of(7, 1));
        }
        if (MedicineUsageTypeEnum.QH.getType().equals(usageInfo)) {
            return FractionUtil.convert2Fraction(dosage).multiplyBy(Fraction.of(24 * 7, 1));
        }
        if (MedicineUsageTypeEnum.Q3H.getType().equals(usageInfo)) {
            return FractionUtil.convert2Fraction(dosage).multiplyBy(Fraction.of(8 * 7, 1));
        }
        if (MedicineUsageTypeEnum.Q4H.getType().equals(usageInfo)) {
            return FractionUtil.convert2Fraction(dosage).multiplyBy(Fraction.of(6 * 7, 1));
        }
        if (MedicineUsageTypeEnum.Q6H.getType().equals(usageInfo)) {
            return FractionUtil.convert2Fraction(dosage).multiplyBy(Fraction.of(4 * 7, 1));
        }
        if (MedicineUsageTypeEnum.Q12H.getType().equals(usageInfo)) {
            return FractionUtil.convert2Fraction(dosage).multiplyBy(Fraction.of(2 * 7, 1));
        }
        if (MedicineUsageTypeEnum.TIW.getType().equals(usageInfo)) {
            return FractionUtil.convert2Fraction(dosage).multiplyBy(Fraction.of(3, 1));
        }
        if (MedicineUsageTypeEnum.Q2W.getType().equals(usageInfo)) {
            return FractionUtil.convert2Fraction(dosage).multiplyBy(Fraction.of(1, 2));
        }
        if (MedicineUsageTypeEnum.Q3W.getType().equals(usageInfo)) {
            return FractionUtil.convert2Fraction(dosage).multiplyBy(Fraction.of(1, 3));
        }
        if (MedicineUsageTypeEnum.Q1M.getType().equals(usageInfo)) {
            return FractionUtil.convert2Fraction(dosage).multiplyBy(Fraction.of(1, 4));
        }

        return Fraction.ZERO;
    }

    public static Fraction calDailyConsumeNum(MedicineInfoEntity medicineInfoEntity, MedicationRecordEntity medicationRecord, Map<Integer, Map<Integer, LocalDateTime>> lastDealTimeMap) {
        try {
            if (medicineInfoEntity == null) {
                return Fraction.ZERO;
            }

            // 获取 record 中用法对应 usageInfo
            List<MedicineRecordDetailDTO> usageDetailList = medicationRecord.getUsageDetail();

            // 计算普通用法的消耗量
            Fraction normalConsumeNum = calRealDailyConsumeNum(medicineInfoEntity.getDosage(), medicineInfoEntity.getUsageInfo(),
                    usageDetailList.get(0), lastDealTimeMap.get(0));

            // 计算特殊用法消耗量
            Fraction specialConsumeNum = Fraction.ZERO;
            for (int i = 0; i < medicineInfoEntity.getExtInfo().getSpecialUsageList().size(); i++) {
                MedicineRecordDetailDTO medicineRecordDetailDTO = usageDetailList.get(i + 1);
                specialConsumeNum.add(calRealDailyConsumeNum(medicineInfoEntity.getDosage(), medicineInfoEntity.getUsageInfo(),
                        medicineRecordDetailDTO, lastDealTimeMap.get(i + 1)));
            }

            return normalConsumeNum.add(specialConsumeNum);
        } catch (Exception e) {
            log.warn(e.getMessage());
            return Fraction.ZERO;
        }
    }

    private static Fraction calRealDailyConsumeNum(String dosage, Integer usageInfo, MedicineRecordDetailDTO usageDetail, Map<Integer, LocalDateTime> lastDealTimeMap) {
        if (usageDetail == null || usageDetail.getUsageInfo() == null) {
            return calDailyConsumeNum(dosage, usageInfo, lastDealTimeMap);
        }
        MedicineUsageDTO recordUsageInfo = usageDetail.getUsageInfo();
        if (StringUtils.isBlank(recordUsageInfo.getDosageInfo()) || Objects.isNull(recordUsageInfo.getUsageType())) {
            return calDailyConsumeNum(dosage, usageInfo, lastDealTimeMap);
        }
        return calDailyConsumeNum(recordUsageInfo.getDosageInfo(), recordUsageInfo.getUsageType(), lastDealTimeMap);
    }

    public static Fraction calDailyConsumeNum(String dosage, Integer usageInfo, Map<Integer, LocalDateTime> lastDealTimeMap) {
        if (MedicineUsageTypeEnum.QD.getType().equals(usageInfo)) {
            return convert2Fraction(dosage).multiplyBy(Fraction.of(1, 1));
        }
        if (MedicineUsageTypeEnum.BID.getType().equals(usageInfo)) {
            return convert2Fraction(dosage).multiplyBy(Fraction.of(2, 1));
        }
        if (MedicineUsageTypeEnum.TID.getType().equals(usageInfo)) {
            return convert2Fraction(dosage).multiplyBy(Fraction.of(3, 1));
        }
        if (MedicineUsageTypeEnum.QID.getType().equals(usageInfo)) {
            return convert2Fraction(dosage).multiplyBy(Fraction.of(4, 1));
        }
        if (MedicineUsageTypeEnum.QOD.getType().equals(usageInfo)) {
            LocalDateTime lastDealTime = lastDealTimeMap.getOrDefault(MedicineUsageTypeEnum.QOD.getType(), LocalDateTime.now());
            if (LocalDateTime.now().isAfter(lastDealTime.toLocalDate().plusDays(2).atStartOfDay())) {
                return convert2Fraction(dosage).multiplyBy(Fraction.of(1, 1));
            } else {
                return Fraction.ZERO;
            }
        }
        if (MedicineUsageTypeEnum.QW.getType().equals(usageInfo)) {
            LocalDateTime lastDealTime = lastDealTimeMap.getOrDefault(MedicineUsageTypeEnum.QW.getType(), LocalDateTime.now());
            if (LocalDateTime.now().isAfter(lastDealTime.toLocalDate().plusDays(7).atStartOfDay())) {
                return convert2Fraction(dosage).multiplyBy(Fraction.of(1, 1));
            } else {
                return Fraction.ZERO;
            }
        }
        if (MedicineUsageTypeEnum.BIW.getType().equals(usageInfo)) {
            LocalDateTime lastDealTime = lastDealTimeMap.getOrDefault(MedicineUsageTypeEnum.BIW.getType(), LocalDateTime.now());
            if (LocalDateTime.now().isAfter(lastDealTime.toLocalDate().plusDays(7).atStartOfDay())) {
                return convert2Fraction(dosage).multiplyBy(Fraction.of(2, 1));
            } else {
                return Fraction.ZERO;
            }
        }
        if (MedicineUsageTypeEnum.Q2H.getType().equals(usageInfo)) {
            return convert2Fraction(dosage).multiplyBy(Fraction.of(12, 1));
        }
        if (MedicineUsageTypeEnum.Q8H.getType().equals(usageInfo)) {
            return convert2Fraction(dosage).multiplyBy(Fraction.of(3, 1));
        }
        if (MedicineUsageTypeEnum.N.getType().equals(usageInfo)) {
            return convert2Fraction(dosage).multiplyBy(Fraction.of(1, 1));
        }
        return Fraction.ZERO;
    }

}
